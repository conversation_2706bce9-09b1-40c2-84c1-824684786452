import { useState, useEffect } from 'react'
import { useThemeStore } from '../store/themeStore'

interface ScrollToTopButtonProps {
  /** Scroll threshold in pixels before button appears */
  threshold?: number
  /** Custom className for styling */
  className?: string
}

function ScrollToTopButton({ threshold = 300, className = '' }: ScrollToTopButtonProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isScrolling, setIsScrolling] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const { isDarkMode } = useThemeStore()

  useEffect(() => {
    let scrollTimeout: NodeJS.Timeout

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const documentHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight

      // Calculate scroll progress (0-100)
      const progress = documentHeight > 0 ? (scrollTop / documentHeight) * 100 : 0
      setScrollProgress(progress)

      // Show/hide button based on scroll position
      setIsVisible(scrollTop > threshold)

      // Add scrolling state for animation
      setIsScrolling(true)
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        setIsScrolling(false)
      }, 150)
    }

    // Add scroll event listener with throttling for better performance
    let ticking = false
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll()
          ticking = false
        })
        ticking = true
      }
    }

    window.addEventListener('scroll', throttledHandleScroll, { passive: true })

    // Check initial scroll position
    handleScroll()

    return () => {
      window.removeEventListener('scroll', throttledHandleScroll)
      clearTimeout(scrollTimeout)
    }
  }, [threshold])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  if (!isVisible) return null

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Progress ring background */}
      <div className="relative">
        <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
          {/* Background circle */}
          <path
            className={isDarkMode ? "stroke-gray-700" : "stroke-gray-300"}
            strokeWidth="2"
            fill="transparent"
            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
          />
          {/* Progress circle */}
          <path
            className={isDarkMode ? "stroke-blue-400" : "stroke-blue-500"}
            strokeWidth="2"
            strokeLinecap="round"
            fill="transparent"
            strokeDasharray={`${scrollProgress}, 100`}
            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
          />
        </svg>

        {/* Button */}
        <button
          type="button"
          onClick={scrollToTop}
          className={`
            absolute inset-0
            w-12 h-12 rounded-full
            flex items-center justify-center
            shadow-lg hover:shadow-xl
            transition-all duration-300 ease-in-out
            transform hover:scale-110 active:scale-95
            ${isScrolling ? 'scale-95' : 'scale-100'}
            ${isDarkMode
              ? 'bg-blue-600 hover:bg-blue-500 text-white shadow-blue-900/25'
              : 'bg-blue-500 hover:bg-blue-600 text-white shadow-blue-500/25'
            }
            ${className}
          `}
          aria-label={`Scroll to top (${Math.round(scrollProgress)}% scrolled)`}
          title={`Back to top (${Math.round(scrollProgress)}% scrolled)`}
        >
          <svg
            className="w-5 h-5 transition-transform duration-200"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2.5}
              d="M5 10l7-7m0 0l7 7m-7-7v18"
            />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default ScrollToTopButton
