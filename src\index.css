@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  color-scheme: dark;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Add smooth transitions for theme changes */
* {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Add these styles to handle mobile menu behavior */
body.menu-open {
  overflow: hidden;
}

/* Ensure smooth transitions for theme changes */
.nav-link {
  transition: all 0.2s ease-in-out;
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Improve touch targets on mobile */
  .nav-link {
    padding: 0.75rem 1rem;
  }

  /* Better touch feedback */
  button,
  .nav-link,
  a {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  }

  /* Prevent zoom on input focus */
  input,
  select,
  textarea {
    font-size: 16px;
  }

  /* Smooth scrolling for mobile */
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Better mobile grid spacing */
  .grid {
    gap: 0.75rem;
  }

  /* Optimize image loading on mobile */
  img {
    image-rendering: optimizeQuality;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .group:hover .group-hover\:opacity-100 {
    opacity: 1;
  }

  /* Always show important controls on touch devices */
  .sm\:opacity-0 {
    opacity: 1 !important;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* Focus improvements for accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading state improvements */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Scroll to top button animations */
@keyframes slideInUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100px);
    opacity: 0;
  }
}

.scroll-to-top-enter {
  animation: slideInUp 0.3s ease-out forwards;
}

.scroll-to-top-exit {
  animation: slideOutDown 0.3s ease-in forwards;
}

/* Text selection */
::selection {
  background-color: rgba(59, 130, 246, 0.3);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hero background image fixes for mobile */
@media (max-width: 640px) {
  .hero-bg {
    background-attachment: scroll !important;
    background-size: cover !important;
    background-position: center 20% !important;
    min-height: 100vh !important;
    min-height: 100dvh !important; /* Dynamic viewport height for mobile */
    width: 100% !important;
    height: 100% !important;
  }

  /* Ensure full coverage on mobile */
  .hero-container {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height */
    min-height: calc(
      100vh + env(safe-area-inset-top)
    ); /* Account for notches */
    width: 100vw;
    position: relative;
  }

  /* Force background to cover entire viewport */
  .hero-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 50%, #374151 100%);
    z-index: -2;
  }
}

/* Fix for iOS Safari viewport issues */
@supports (-webkit-touch-callout: none) {
  .hero-container {
    min-height: -webkit-fill-available;
  }
}

/* Additional mobile viewport fixes */
@media screen and (max-width: 640px) {
  /* Prevent horizontal scroll */
  .hero-container {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Ensure background images fill completely */
  .hero-bg,
  .hero-container > div[style*="background-image"] {
    background-size: cover !important;
    background-repeat: no-repeat !important;
    background-position: center 25% !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
  }
}

/* Landscape mobile orientation */
@media screen and (max-width: 640px) and (orientation: landscape) {
  .hero-bg {
    background-position: center center !important;
  }
}
