<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-MPCBJDMQZV"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-MPCBJDMQZV');
    </script>

    <!-- Favicon links -->
    <link rel="icon" type="image/svg+xml" href="/MovieHub/icons/favicon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="/MovieHub/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/MovieHub/icons/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/MovieHub/icons/apple-touch-icon.png">
    <link rel="manifest" href="/MovieHub/icons/site.webmanifest">
    <meta name="theme-color" content="#1E293B">

    <title>Movie Explorer</title>

    <script>
      // GitHub Pages SPA redirect handling
      // This script handles direct navigation to any route in the SPA

      // Get the current path that was requested
      const currentPath = location.pathname;
      const search = location.search;
      const hash = location.hash;

      // Check if this is a route within our app (starts with /MovieHub/)
      if (currentPath.startsWith('/MovieHub/') && currentPath !== '/MovieHub/') {
        // Extract the route part (everything after /MovieHub)
        const route = currentPath.substring('/MovieHub'.length);

        // Store the full route information for the app to handle
        sessionStorage.setItem('spa-redirect', JSON.stringify({
          pathname: route,
          search: search,
          hash: hash
        }));
      }

      // Always redirect to the base URL so React Router can take over
      location.replace('/MovieHub/');
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/MovieHub/src/main.tsx"></script>
  </body>
</html>
